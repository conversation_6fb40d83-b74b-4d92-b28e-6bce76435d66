"""Queue service using <PERSON>ey with SQLite backend for background task processing."""
import logging
import os
from huey import Sqlite<PERSON>uey
from pathlib import Path

# Configure logger
logger = logging.getLogger(__name__)

# Create queue directory if it doesn't exist
QUEUE_DIR = Path("queue_data")
QUEUE_DIR.mkdir(exist_ok=True)

# SQLite database path for queue
QUEUE_DB_PATH = QUEUE_DIR / "huey_queue.db"

# Initialize Huey with SQLite backend
huey = SqliteHuey(
    name='file_processor_queue',
    filename=str(QUEUE_DB_PATH),
    immediate=False,  # Set to True for testing, False for production
    utc=True
)

logger.info(f"Huey queue initialized with SQLite database: {QUEUE_DB_PATH}")


@huey.task()
def test_worker_task():
    """Test task to verify worker is running."""
    logger.info("TEST TASK: Worker is alive and processing tasks!")
    return "Worker is working!"


@huey.task()
def process_file_task(
    file_id: str,
    url: str,
    chunk_size: int,
    chunk_overlap: int,
    vector_store_id: str = None
):
    """
    Background task for processing a file using Huey.

    Args:
        file_id (str): ID of the file
        url (str): URL of the file to download and process
        chunk_size (int): Size of each chunk in characters
        chunk_overlap (int): Overlap between chunks in characters
        vector_store_id (str): ID of the vector store to associate chunks with
    """
    import time
    from sqlalchemy.orm import Session
    from core.database import get_db_session
    from services import s3_file_service, document_chunks_service, markdown_service
    from utils.cleanup import cleanup_temp_file

    logger.info(f"STARTING BACKGROUND PROCESSING for file {file_id}")
    logger.info(f"Task parameters: url={url}, chunk_size={chunk_size}, chunk_overlap={chunk_overlap}, vector_store_id={vector_store_id}")

    # Get database session
    db = get_db_session()
    temp_file_path = None

    try:
        # Update progress tracking in Redis/Database
        logger.info(f"STEP 1: Updating progress to 5% - Checking URL")
        update_task_progress(file_id, 5, "Đang kiểm tra URL")

        # Kiểm tra vector_store_id nếu được cung cấp
        logger.info(f"STEP 2: Validating vector_store_id")
        if not vector_store_id:
            vector_store_id = "vs_default"
            logger.info(f"No vector store ID provided, using default: {vector_store_id}")
        else:
            # Kiểm tra định dạng vector_store_id
            if not vector_store_id.startswith("vs_"):
                logger.warning(f"Invalid vector store ID format: {vector_store_id}, using default")
                vector_store_id = "vs_default"
            else:
                logger.info(f"Using vector store ID: {vector_store_id}")

        # Validate URL and get metadata
        logger.info(f"STEP 3: Validating URL: {url}")
        is_valid, metadata = s3_file_service.validate_s3_url(url)
        if not is_valid:
            logger.error(f"URL validation failed: {url}")
            update_task_progress(file_id, 100, "Lỗi: URL không hợp lệ hoặc không thể truy cập", "Không thể tải file từ URL")
            return {"status": "error", "message": "Invalid URL"}

        logger.info(f"URL validation successful. Metadata: {metadata}")

        # Update progress - Starting download
        logger.info(f"STEP 4: Starting file download")
        update_task_progress(file_id, 10, "Đang tải tập tin từ URL")

        # Download file to temporary location
        logger.info(f"Downloading file from URL: {url}")
        temp_file_path = s3_file_service.download_file_to_temp(
            file_id=file_id,
            url=url
        )
        logger.info(f"File downloaded to: {temp_file_path}")

        # Update progress - Starting conversion
        logger.info(f"STEP 5: Starting markdown conversion")
        update_task_progress(file_id, 30, "Đang chuyển đổi tập tin sang Markdown")

        # Convert file to markdown
        logger.info(f"Converting file to markdown: {temp_file_path}")
        markdown_content = markdown_service.convert_file_to_markdown(temp_file_path)
        if not markdown_content:
            logger.error(f"Markdown conversion failed for file: {temp_file_path}")
            update_task_progress(file_id, 100, "Lỗi: Không thể chuyển đổi tập tin sang Markdown", "Tập tin không có nội dung hoặc không hỗ trợ định dạng")
            return {"status": "error", "message": "Conversion failed"}

        logger.info(f"Markdown conversion successful. Content length: {len(markdown_content)} characters")

        # Update progress - Starting chunking
        update_task_progress(file_id, 50, "Đang chia nhỏ nội dung")

        # Create chunks
        chunks = document_chunks_service.create_chunks_for_file(
            db=db,
            file_id=file_id,
            markdown_content=markdown_content,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            vector_store_id=vector_store_id
        )

        if not chunks:
            update_task_progress(file_id, 100, "Lỗi: Không thể tạo chunks từ nội dung", "Nội dung không đủ để tạo chunks")
            return {"status": "error", "message": "No chunks created"}

        # Update progress - Processing embeddings
        update_task_progress(file_id, 70, f"Đang tạo embeddings cho {len(chunks)} chunks")

        # Process embeddings for chunks
        logger.info(f"Creating embeddings for {len(chunks)} chunks")

        # Get chunk IDs for embedding creation
        chunk_ids = [str(chunk.id) for chunk in chunks]

        # Create embeddings using the correct function
        embedding_results = document_chunks_service.create_embeddings_for_chunks(
            db=db,
            chunk_ids=chunk_ids,
            batch_size=10,
            max_workers=3  # Reduce workers to avoid overwhelming the API
        )

        logger.info(f"Embedding creation results: {embedding_results}")
        processed_chunks = chunks  # Use original chunks since embeddings are updated in place

        # Update progress - Finalizing
        update_task_progress(file_id, 90, "Đang hoàn tất xử lý")

        # Update file record with final information
        s3_file_service.update_file_processing_status(
            db=db,
            file_id=file_id,
            status="completed",
            chunks_count=len(processed_chunks)
        )

        # Final progress update
        update_task_progress(file_id, 100, f"Hoàn tất xử lý - Đã tạo {len(processed_chunks)} chunks")

        logger.info(f"SUCCESSFULLY PROCESSED file {file_id} with {len(processed_chunks)} chunks")

        return {
            "status": "completed",
            "file_id": file_id,
            "chunks_count": len(processed_chunks),
            "message": "File processed successfully"
        }

    except Exception as e:
        logger.error(f"ERROR processing file {file_id}: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        update_task_progress(file_id, 100, f"Lỗi xử lý: {str(e)}", "Có lỗi xảy ra trong quá trình xử lý")
        return {"status": "error", "message": str(e)}

    finally:
        # Cleanup temporary file
        if temp_file_path:
            logger.info(f"Cleaning up temporary file: {temp_file_path}")
            cleanup_temp_file(temp_file_path)

        # Close database session
        if db:
            logger.info(f"Closing database session")
            db.close()

        logger.info(f"TASK COMPLETED for file {file_id}")


def update_task_progress(file_id: str, progress: int, message: str, details: str = None):
    """
    Update task progress. This will be stored in SQLite database.

    Args:
        file_id (str): File ID
        progress (int): Progress percentage (0-100)
        message (str): Progress message
        details (str): Additional details
    """
    import time
    import sqlite3

    try:
        # Use SQLite to store progress
        progress_db_path = QUEUE_DIR / "progress.db"

        with sqlite3.connect(str(progress_db_path)) as conn:
            # Create table if not exists
            conn.execute("""
                CREATE TABLE IF NOT EXISTS task_progress (
                    file_id TEXT PRIMARY KEY,
                    status TEXT,
                    progress INTEGER,
                    message TEXT,
                    details TEXT,
                    timestamp INTEGER
                )
            """)

            # Insert or update progress
            conn.execute("""
                INSERT OR REPLACE INTO task_progress
                (file_id, status, progress, message, details, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                file_id,
                "processing" if progress < 100 else "completed",
                progress,
                message,
                details,
                int(time.time() * 1000)
            ))

        logger.info(f"Progress update for {file_id}: {progress}% - {message}")

    except Exception as e:
        logger.error(f"Error updating progress for {file_id}: {str(e)}")


def get_task_progress(file_id: str) -> dict:
    """
    Get task progress from SQLite database.

    Args:
        file_id (str): File ID

    Returns:
        dict: Progress information
    """
    import sqlite3

    try:
        progress_db_path = QUEUE_DIR / "progress.db"

        with sqlite3.connect(str(progress_db_path)) as conn:
            cursor = conn.execute("""
                SELECT status, progress, message, details, timestamp
                FROM task_progress
                WHERE file_id = ?
            """, (file_id,))

            row = cursor.fetchone()
            if row:
                return {
                    "file_id": file_id,
                    "status": row[0],
                    "progress": row[1],
                    "message": row[2],
                    "details": row[3],
                    "timestamp": row[4]
                }
            else:
                return {
                    "file_id": file_id,
                    "status": "not_found",
                    "progress": 0,
                    "message": "Task not found",
                    "details": None,
                    "timestamp": None
                }

    except Exception as e:
        logger.error(f"Error getting progress for {file_id}: {str(e)}")
        return {
            "file_id": file_id,
            "status": "error",
            "progress": 0,
            "message": f"Error retrieving progress: {str(e)}",
            "details": None,
            "timestamp": None
        }


def queue_file_processing(
    file_id: str,
    url: str,
    chunk_size: int,
    chunk_overlap: int,
    vector_store_id: str = None
) -> str:
    """
    Queue a file processing task.

    Args:
        file_id (str): File ID
        url (str): File URL
        chunk_size (int): Chunk size
        chunk_overlap (int): Chunk overlap
        vector_store_id (str): Vector store ID

    Returns:
        str: Task ID
    """
    try:
        logger.info(f"QUEUEING FILE PROCESSING TASK")
        logger.info(f"Parameters: file_id={file_id}, url={url}")
        logger.info(f"Chunk settings: size={chunk_size}, overlap={chunk_overlap}")
        logger.info(f"Vector store: {vector_store_id}")

        # Queue the task
        logger.info(f"Adding task to Huey queue...")
        task = process_file_task(
            file_id=file_id,
            url=url,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            vector_store_id=vector_store_id
        )

        # Initialize progress
        update_task_progress(file_id, 0, "Đã tạo file record, chuẩn bị xử lý")

        logger.info(f"SUCCESSFULLY QUEUED task for {file_id}, task ID: {task.id}")
        logger.info(f"Current queue size: {len(huey)}")
        return str(task.id)

    except Exception as e:
        logger.error(f"Error queueing task: {str(e)}")
        raise


def test_worker_connection():
    """Test if worker is running by sending a test task."""
    try:
        logger.info("Testing worker connection...")
        task = test_worker_task()
        logger.info(f"Test task queued with ID: {task.id}")
        return True
    except Exception as e:
        logger.error(f"Error testing worker: {str(e)}")
        return False
